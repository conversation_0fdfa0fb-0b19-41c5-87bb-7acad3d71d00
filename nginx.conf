load_module /usr/lib/nginx/modules/ngx_stream_module.so;
worker_processes auto;
events {
    worker_connections  1024;
    accept_mutex on;
  }
http {
  keepalive_timeout 75s;
  gzip on;
  gzip_min_length 4k;
  gzip_comp_level 4;
  client_max_body_size 1024m;
  client_header_buffer_size 32k;
  client_body_buffer_size 8m;
  server_names_hash_bucket_size 512;
  proxy_headers_hash_max_size 51200;
  proxy_headers_hash_bucket_size 6400;
  gzip_types application/javascript application/x-javascript text/javascript text/css application/json application/xml;
  include mime.types;
  default_type application/octet-stream;
  error_log /home/<USER>/log/error.log;
  access_log /home/<USER>/log/access.log;
  server {
    server_name eeclat.cn;
    listen 443 ssl;
    ssl_certificate /home/<USER>/cert/eeclat.cn/eeclat.cn_bundle.crt;
    ssl_certificate_key /home/<USER>/cert/eeclat.cn/eeclat.cn.key;
    ssl_protocols TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
    listen 80;
    if ($scheme = http) {
      return 301 https://$host:443$request_uri;
    }
    location /prototyping {
      alias /html/prototyping/;
    }
    location /authsage {
      alias /html/frontend_dev_authsage/;
      try_files $uri $uri/ /authsage/index.html;
    }
    location /authsage/api/ {
      proxy_pass http://*********:14001/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Host $http_host;
      proxy_set_header X-Forwarded-Port $server_port;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_redirect http:// https://;
    }
    location /ops_management {
      alias /html/frontend_dev_ops_management/;
      try_files $uri $uri/ /ops_management/index.html;
    }
    location /ops_management/api/ {
      proxy_pass http://*********:14010/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Host $http_host;
      proxy_set_header X-Forwarded-Port $server_port;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_redirect http:// https://;
    }
    location ~ \.txt$ {
      root /html/;
    }
    location /jump_mp {
      alias /html/h5_jump_mp/;
    }
    location / {
      proxy_pass http://*********:14010/;
      proxy_set_header Host $host;
      proxy_set_header X-Real-IP $remote_addr;
      proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
      proxy_set_header X-Forwarded-Host $http_host;
      proxy_set_header X-Forwarded-Port $server_port;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_redirect http:// https://;
    }
  }
}
