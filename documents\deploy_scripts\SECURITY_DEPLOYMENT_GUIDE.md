# 🔐 安全部署指南 - 环境变量管理

## 概述

本指南介绍如何安全地管理和部署智能处置运营管理系统的敏感环境变量，避免密钥泄露风险。

## 🚨 安全风险警告

**已修复的安全问题：**
- ❌ 配置文件中的明文密钥存储
- ❌ Supervisor配置中的硬编码密钥
- ❌ 缺乏环境变量隔离机制

## 📋 部署前准备

### 1. 环境变量文件设置

```bash
# 1. 进入部署目录
cd /file_server/htm/ops_management_htm

# 2. 复制环境变量模板
cp .env.template .env

# 3. 编辑生产环境配置
vim .env

# 4. 设置安全权限（重要！）
chmod 600 .env
chown root:root .env
```

### 2. 必需配置项检查清单

**高敏感度配置（必须设置）：**
- [ ] `TENCENTCLOUD_SECRET_ID` - 腾讯云API密钥ID
- [ ] `TENCENTCLOUD_SECRET_KEY` - 腾讯云API密钥
- [ ] `DB_PASSWORD` - 数据库密码
- [ ] `CLIENT_SECRET` - OAuth2客户端密钥

**中敏感度配置：**
- [ ] `DB_HOST`, `DB_USER`, `DB_NAME` - 数据库连接信息
- [ ] `REDIS_HOST`, `REDIS_PORT` - Redis连接信息
- [ ] `AUTH_SERVER_URL` - 认证服务器地址

## 🚀 部署步骤

### 1. 使用更新后的部署脚本

```bash
# 执行安全部署脚本
sudo bash documents/deploy_scripts/ops_management_htm_run.sh
```

脚本会自动：
- ✅ 检查环境变量文件是否存在
- ✅ 验证文件权限安全性
- ✅ 使用 `--env-file` 安全注入环境变量
- ✅ 提供详细的部署日志

### 2. 验证部署结果

```bash
# 检查容器状态
sudo docker ps | grep ops-management-htm-container

# 查看容器日志
sudo docker logs ops-management-htm-container

# 验证环境变量注入（不会显示敏感值）
sudo docker exec ops-management-htm-container env | grep -E "TENCENTCLOUD|DB_|CLIENT_"
```

## 🔒 安全最佳实践

### 1. 文件权限管理

```bash
# 环境变量文件权限
chmod 600 .env          # 仅所有者可读写
chown root:root .env     # 设置所有者为root

# 部署脚本权限
chmod 750 documents/deploy_scripts/ops_management_htm_run.sh
```

### 2. 密钥轮换策略

**建议轮换周期：**
- 🔴 腾讯云API密钥：每90天
- 🟡 数据库密码：每180天
- 🟡 OAuth2客户端密钥：每180天

**轮换步骤：**
1. 在云服务商控制台生成新密钥
2. 更新 `.env` 文件
3. 重启容器：`sudo docker restart ops-management-htm-container`
4. 验证服务正常运行
5. 删除旧密钥

### 3. 监控和审计

```bash
# 监控环境变量文件访问
sudo auditctl -w /file_server/htm/ops_management_htm/.env -p rwxa

# 检查文件完整性
sudo sha256sum /file_server/htm/ops_management_htm/.env
```

## 🚨 应急响应

### 密钥泄露处理流程

1. **立即响应（5分钟内）**
   ```bash
   # 停止容器
   sudo docker stop ops-management-htm-container
   
   # 撤销泄露的密钥（在云服务商控制台）
   ```

2. **密钥更换（30分钟内）**
   ```bash
   # 生成新密钥并更新配置
   vim /file_server/htm/ops_management_htm/.env
   
   # 重新部署
   sudo bash documents/deploy_scripts/ops_management_htm_run.sh
   ```

3. **验证和监控（持续）**
   - 检查系统日志异常访问
   - 监控API调用量是否异常
   - 验证所有服务功能正常

## 📞 技术支持

如遇到部署问题，请检查：
1. 环境变量文件格式是否正确
2. 所有必需变量是否已设置
3. 文件权限是否符合安全要求
4. Docker容器日志中的错误信息

---

**安全提醒：** 请定期审查和更新安全配置，确保系统始终处于安全状态。
